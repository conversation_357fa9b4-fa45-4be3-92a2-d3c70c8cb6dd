'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ParticleBackground from '@/components/animations/ParticleBackground';
import { Search, Filter, TrendingUp, Zap, Eye, BarChart3, Clock, Target, Layers } from 'lucide-react';

interface Technology {
  name: string;
  description: string;
  maturity: 'emerging' | 'growing' | 'maturing';
  category: string;
  growthRate: string;
  relevanceScore: number;
  industryImpact: Record<string, string>;
  businessApplications?: string[];
  predictedGrowth?: string;
  keyPlayers?: string[];
}

interface PulseSectionProps {
  translations?: {
    title?: string;
    subtitle?: string;
    howToUseTitle?: string;
    howToUseStep1Title?: string;
    howToUseStep1Desc?: string;
    howToUseStep2Title?: string;
    howToUseStep2Desc?: string;
    howToUseStep3Title?: string;
    howToUseStep3Desc?: string;
    timeRangeSelector?: {
      title?: string;
      impactHorizonLabel?: string;
      options?: {
        sixMonths?: string;
        sixMonthsDesc?: string;
        oneYear?: string;
        oneYearDesc?: string;
        fiveYears?: string;
        fiveYearsDesc?: string;
      };
    };
    filters?: {
      title?: string;
      description?: string;
      selectedLabel?: string;
      options?: {
        ai?: string;
        aiDesc?: string;
        cloud?: string;
        cloudDesc?: string;
        iot?: string;
        iotDesc?: string;
        security?: string;
        securityDesc?: string;
        blockchain?: string;
        blockchainDesc?: string;
      };
    };
    industryFilter?: {
      title?: string;
      description?: string;
      placeholder?: string;
      options?: {
        all?: string;
        lifeInsurance?: string;
        technologyServices?: string;
        manufacturing?: string;
        healthcare?: string;
        finance?: string;
        energy?: string;
      };
    };
    maturityLevels?: {
      title?: string;
      established?: string;
      maturing?: string;
      growing?: string;
      emerging?: string;
      descriptions?: {
        established?: string;
        maturing?: string;
        growing?: string;
        emerging?: string;
      };
    };
    technologies?: Record<string, Technology>;
    timelineTitle?: string;
    timelineDescription?: string;
    timelineIndustryLabel?: string;
    technologiesLabel?: string;
    noTechnologiesMessage?: string;
    noTechnologiesIndustryMessage?: string;
    resetFiltersLabel?: string;
    technologyDetails?: {
      businessImpactTab?: string;
      technicalDetailsTab?: string;
      industryImpactLabel?: string;
      industryImpactDescription?: string;
      businessApplicationsLabel?: string;
      useCasesLabel?: string;
      keyPlayersLabel?: string;
      predictedGrowthLabel?: string;
    };
    relevanceMeter?: {
      title?: string;
      description?: string;
      companySize?: {
        title?: string;
        description?: string;
        options?: {
          startup?: string;
          smb?: string;
          enterprise?: string;
        };
      };
      industry?: {
        title?: string;
        description?: string;
        selectPlaceholder?: string;
        options?: Record<string, string>;
      };
      relevanceScore?: {
        title?: string;
        lowLabel?: string;
        highLabel?: string;
        descriptions?: {
          high?: string;
          good?: string;
          moderate?: string;
          low?: string;
        };
      };
      cta?: string;
      ctaDescription?: string;
      ctaTitle?: string;
      footerText?: string;
    };
  };
}

const PulseSection: React.FC<PulseSectionProps> = ({ translations }) => {
  // State management for the dashboard
  const [selectedIndustry, setSelectedIndustry] = useState<string>('lifeInsurance');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [timeHorizon, setTimeHorizon] = useState<string>('6months');
  const [maturityFilters, setMaturityFilters] = useState<string[]>(['emerging', 'growing', 'maturing']);
  const [businessImpact, setBusinessImpact] = useState<number>(87);
  const [selectedTech, setSelectedTech] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Define maturity level colors consistent with design system
  const maturityColors = {
    emerging: '#10b981', // green
    growing: '#f59e0b', // amber
    maturing: '#3b82f6', // blue
  };

  // Sample technology data for Life Insurance
  const sampleTechnologies: Technology[] = [
    {
      name: 'Digital Health Underwriting',
      description: 'AI-powered health assessment using wearables and lifestyle data',
      maturity: 'emerging',
      category: 'AI/ML',
      growthRate: '78%',
      relevanceScore: 95,
      industryImpact: { lifeInsurance: 'Very High', techServices: 'Medium', manufacturing: 'Low' },
      businessApplications: ['Risk assessment automation', 'Real-time health monitoring', 'Personalized premium pricing'],
      predictedGrowth: '89% by 2025',
      keyPlayers: ['John Hancock', 'Vitality', 'Oscar Health']
    },
    {
      name: 'Parametric Life Products',
      description: 'Smart contracts trigger automatic payouts based on predefined parameters',
      maturity: 'growing',
      category: 'Blockchain',
      growthRate: '89%',
      relevanceScore: 88,
      industryImpact: { lifeInsurance: 'High', techServices: 'Medium', manufacturing: 'Low' },
      businessApplications: ['Instant claim processing', 'Reduced fraud', 'Lower operational costs'],
      predictedGrowth: '156% by 2025',
      keyPlayers: ['Etherisc', 'Nexus Mutual', 'InsurAce']
    },
    {
      name: 'Federated Learning',
      description: 'Privacy-preserving collaborative machine learning across institutions',
      maturity: 'emerging',
      category: 'AI/ML',
      growthRate: '92%',
      relevanceScore: 82,
      industryImpact: { lifeInsurance: 'High', techServices: 'High', manufacturing: 'Medium' },
      businessApplications: ['Cross-industry risk models', 'Privacy-compliant data sharing', 'Enhanced fraud detection'],
      predictedGrowth: '134% by 2025',
      keyPlayers: ['Google', 'IBM', 'NVIDIA']
    },
    {
      name: 'Causal AI Systems',
      description: 'AI that understands cause-effect relationships for reliable predictions',
      maturity: 'emerging',
      category: 'AI/ML',
      growthRate: '67%',
      relevanceScore: 79,
      industryImpact: { lifeInsurance: 'High', techServices: 'Medium', manufacturing: 'Medium' },
      businessApplications: ['Mortality prediction', 'Policy optimization', 'Risk factor analysis'],
      predictedGrowth: '98% by 2025',
      keyPlayers: ['Causality', 'Microsoft', 'Amazon']
    },
    {
      name: 'Quantum-Safe Cryptography',
      description: 'Post-quantum security algorithms protecting against future threats',
      maturity: 'maturing',
      category: 'Security',
      growthRate: '45%',
      relevanceScore: 75,
      industryImpact: { lifeInsurance: 'High', techServices: 'Very High', manufacturing: 'Medium' },
      businessApplications: ['Data protection', 'Secure communications', 'Future-proof encryption'],
      predictedGrowth: '67% by 2025',
      keyPlayers: ['IBM', 'Google', 'NIST']
    },
    {
      name: 'Neural Process Automation',
      description: 'AI learns and automates complex business processes automatically',
      maturity: 'growing',
      category: 'AI/ML',
      growthRate: '73%',
      relevanceScore: 85,
      industryImpact: { lifeInsurance: 'Very High', techServices: 'High', manufacturing: 'High' },
      businessApplications: ['Claims processing', 'Customer service', 'Policy administration'],
      predictedGrowth: '112% by 2025',
      keyPlayers: ['UiPath', 'Automation Anywhere', 'Blue Prism']
    },
    {
      name: 'Digital Twin Ecosystems',
      description: 'Interconnected digital replicas for comprehensive optimization',
      maturity: 'maturing',
      category: 'IoT',
      growthRate: '58%',
      relevanceScore: 71,
      industryImpact: { lifeInsurance: 'Medium', techServices: 'High', manufacturing: 'Very High' },
      businessApplications: ['Customer behavior modeling', 'Product simulation', 'Risk scenario testing'],
      predictedGrowth: '87% by 2025',
      keyPlayers: ['Siemens', 'GE Digital', 'Microsoft']
    }
  ];

  // Filter technologies based on current selections
  const getFilteredTechnologies = () => {
    return sampleTechnologies.filter(tech => {
      // Industry filter
      const industryMatch = !selectedIndustry ||
        tech.industryImpact[selectedIndustry] === 'High' ||
        tech.industryImpact[selectedIndustry] === 'Very High';

      // Maturity filter
      const maturityMatch = maturityFilters.includes(tech.maturity);

      // Search filter
      const searchMatch = !searchQuery ||
        tech.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tech.description.toLowerCase().includes(searchQuery.toLowerCase());

      return industryMatch && maturityMatch && searchMatch;
    });
  };

  // Handle technology selection
  const handleTechSelect = (techName: string) => {
    setSelectedTech(selectedTech === techName ? null : techName);
  };

  // Handle maturity filter toggle
  const toggleMaturityFilter = (maturity: string) => {
    if (maturityFilters.includes(maturity)) {
      setMaturityFilters(maturityFilters.filter(m => m !== maturity));
    } else {
      setMaturityFilters([...maturityFilters, maturity]);
    }
  };

  // Industry options
  const industryOptions = [
    { key: 'lifeInsurance', label: 'Life Insurance', icon: '🏥' },
    { key: 'techServices', label: 'Tech Services', icon: '💻' },
    { key: 'manufacturing', label: 'Manufacturing', icon: '🏭' }
  ];

  // Time horizon options
  const timeHorizonOptions = [
    { key: '6months', label: '6 Months', description: 'Immediate impact' },
    { key: '1year', label: '1 Year', description: 'Short-term adoption' },
    { key: '5years', label: '5 Years', description: 'Long-term transformation' }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };



  return (
    <div className="bg-[#121826] text-white font-['Montserrat',sans-serif]">
      {/* Background particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <ParticleBackground
            color="#4FC3F7"
            secondaryColor="#7C4DFF"
            density="medium"
            speed="slow"
          />
        </div>
      </div>

      {/* Header Section */}
      <section ref={ref} className="relative" id="pulse-section" style={{ marginTop: 'var(--nav-sticky-top)' }}>
        <div className="bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-sm border-b border-gray-700/50">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Title and Subtitle */}
              <div className="flex-1">
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                  Technology Pulse
                </h1>
                <p className="text-gray-300 text-lg">
                  Real-time emerging tech tracker
                </p>
              </div>

              {/* Industry Filter Pills */}
              <div className="flex flex-wrap gap-3">
                {industryOptions.map((industry) => (
                  <button
                    key={industry.key}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                      selectedIndustry === industry.key
                        ? 'bg-[#4FC3F7] text-white shadow-lg shadow-[#4FC3F7]/25'
                        : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                    }`}
                    onClick={() => setSelectedIndustry(industry.key)}
                  >
                    <span className="mr-2">{industry.icon}</span>
                    {industry.label}
                  </button>
                ))}
              </div>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search technologies..."
                  className="pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4FC3F7] focus:border-transparent w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard Layout */}
        <div className="container mx-auto px-4 py-8">
          <div className="flex gap-8">
            {/* Left Sidebar - 280px width */}
            <motion.div
              className="w-[280px] flex-shrink-0"
              initial={{ opacity: 0, x: -20 }}
              animate={isIntersecting ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="sticky top-24 space-y-6">
                {/* Time Horizon Selector */}
                <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Clock className="w-4 h-4 text-[#4FC3F7]" />
                    Time Horizon
                  </h3>
                  <div className="bg-gray-800/50 rounded-lg p-1 flex">
                    {timeHorizonOptions.map((option) => (
                      <button
                        key={option.key}
                        className={`flex-1 px-3 py-2 rounded-md text-xs font-medium transition-all ${
                          timeHorizon === option.key
                            ? 'bg-[#4FC3F7] text-white shadow-lg'
                            : 'text-gray-400 hover:text-white'
                        }`}
                        onClick={() => setTimeHorizon(option.key)}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Maturity Level Filters */}
                <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Filter className="w-4 h-4 text-[#4FC3F7]" />
                    Maturity Level
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(maturityColors).map(([key, color]) => (
                      <label key={key} className="flex items-center gap-3 cursor-pointer group">
                        <input
                          type="checkbox"
                          checked={maturityFilters.includes(key)}
                          onChange={() => toggleMaturityFilter(key)}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-all ${
                          maturityFilters.includes(key)
                            ? 'border-transparent'
                            : 'border-gray-600 group-hover:border-gray-500'
                        }`} style={{ backgroundColor: maturityFilters.includes(key) ? color : 'transparent' }}>
                          {maturityFilters.includes(key) && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </div>
                        <span className="text-gray-300 text-sm capitalize group-hover:text-white transition-colors">
                          {key}
                        </span>
                        <span className="text-xs text-gray-500 ml-auto">
                          ({sampleTechnologies.filter(t => t.maturity === key).length})
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Business Impact Meter */}
                <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Target className="w-4 h-4 text-[#4FC3F7]" />
                    Business Impact
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-300">Relevance Score</span>
                      <span className="text-lg font-bold text-[#4FC3F7]">{businessImpact}%</span>
                    </div>
                    <div className="w-full h-3 bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] rounded-full transition-all duration-500"
                        style={{ width: `${businessImpact}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-400">
                      High impact for {industryOptions.find(i => i.key === selectedIndustry)?.label}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Main Content Area */}
            <motion.div
              className="flex-1"
              initial={{ opacity: 0 }}
              animate={isIntersecting ? { opacity: 1 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {/* Content Header */}
              <div className="bg-gradient-to-r from-gray-900/70 to-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-white">
                    Trending Technologies
                  </h2>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-300">
                      {getFilteredTechnologies().length} technologies match your filters
                    </span>
                    <div className="flex items-center gap-2">
                      <button
                        className={`p-2 rounded-lg transition-all ${
                          viewMode === 'grid'
                            ? 'bg-[#4FC3F7] text-white'
                            : 'bg-gray-700 text-gray-400 hover:text-white'
                        }`}
                        onClick={() => setViewMode('grid')}
                      >
                        <Layers className="w-4 h-4" />
                      </button>
                      <button
                        className={`p-2 rounded-lg transition-all ${
                          viewMode === 'list'
                            ? 'bg-[#4FC3F7] text-white'
                            : 'bg-gray-700 text-gray-400 hover:text-white'
                        }`}
                        onClick={() => setViewMode('list')}
                      >
                        <BarChart3 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Technology Cards Grid */}
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate={isIntersecting ? "visible" : "hidden"}
                className="space-y-6"
              >
                {getFilteredTechnologies().length === 0 ? (
                  <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-12 text-center">
                    <div className="text-gray-400 mb-4 text-lg">
                      No technologies match your current filters
                    </div>
                    <button
                      className="px-6 py-3 bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] text-white rounded-lg font-medium hover:opacity-90 transition-opacity"
                      onClick={() => {
                        setMaturityFilters(['emerging', 'growing', 'maturing']);
                        setSelectedIndustry('lifeInsurance');
                        setSearchQuery('');
                      }}
                    >
                      Reset Filters
                    </button>
                  </div>
                ) : viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {getFilteredTechnologies().map((tech, index) => (
                      <motion.div
                        key={tech.name}
                        variants={itemVariants}
                        className="group"
                      >
                        <div className={`bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 hover:border-[#4FC3F7]/50 transition-all duration-300 cursor-pointer ${
                          selectedTech === tech.name ? 'ring-2 ring-[#4FC3F7] border-[#4FC3F7]' : ''
                        }`}
                        onClick={() => handleTechSelect(tech.name)}
                        >
                          {/* Card Header */}
                          <div className="flex justify-between items-start mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: maturityColors[tech.maturity] }}
                                ></div>
                                <h3 className="text-lg font-semibold text-white group-hover:text-[#4FC3F7] transition-colors">
                                  {tech.name}
                                </h3>
                              </div>
                              <p className="text-gray-300 text-sm leading-relaxed">
                                {tech.description}
                              </p>
                            </div>
                            <div className="ml-4 text-right">
                              <div className="text-2xl font-bold text-[#4FC3F7] mb-1">
                                {tech.growthRate}
                              </div>
                              <div className="text-xs text-gray-400 uppercase tracking-wide">
                                Growth
                              </div>
                            </div>
                          </div>

                          {/* Metrics */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-gray-800/50 rounded-lg p-3">
                              <div className="text-xs text-gray-400 mb-1">Relevance Score</div>
                              <div className="flex items-center gap-2">
                                <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                                  <div
                                    className="h-full bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] rounded-full"
                                    style={{ width: `${tech.relevanceScore}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium text-white">{tech.relevanceScore}%</span>
                              </div>
                            </div>
                            <div className="bg-gray-800/50 rounded-lg p-3">
                              <div className="text-xs text-gray-400 mb-1">Maturity</div>
                              <div className="text-sm font-medium text-white capitalize">
                                {tech.maturity}
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex gap-3">
                            <button className="flex-1 bg-[#4FC3F7] text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-[#4FC3F7]/90 transition-colors flex items-center justify-center gap-2">
                              <Eye className="w-4 h-4" />
                              Explore
                            </button>
                            <button className="bg-gray-700/50 text-gray-300 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-600/50 transition-colors">
                              Compare
                            </button>
                          </div>

                          {/* Expanded Details */}
                          {selectedTech === tech.name && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="mt-6 pt-6 border-t border-gray-700"
                            >
                              <div className="space-y-4">
                                {/* Business Applications */}
                                {tech.businessApplications && (
                                  <div>
                                    <h4 className="text-sm font-medium text-white mb-2">Business Applications</h4>
                                    <ul className="space-y-1">
                                      {tech.businessApplications.map((app, i) => (
                                        <li key={i} className="text-xs text-gray-300 flex items-center gap-2">
                                          <div className="w-1 h-1 bg-[#4FC3F7] rounded-full"></div>
                                          {app}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}

                                {/* Key Players */}
                                {tech.keyPlayers && (
                                  <div>
                                    <h4 className="text-sm font-medium text-white mb-2">Key Players</h4>
                                    <div className="flex flex-wrap gap-2">
                                      {tech.keyPlayers.map((player, i) => (
                                        <span key={i} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded">
                                          {player}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  // List View
                  <div className="space-y-4">
                    {getFilteredTechnologies().map((tech, index) => (
                      <motion.div
                        key={tech.name}
                        variants={itemVariants}
                        className="bg-gradient-to-r from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 hover:border-[#4FC3F7]/50 transition-all duration-300"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 flex-1">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: maturityColors[tech.maturity] }}
                            ></div>
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-white">{tech.name}</h3>
                              <p className="text-gray-300 text-sm">{tech.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-6">
                            <div className="text-center">
                              <div className="text-lg font-bold text-[#4FC3F7]">{tech.growthRate}</div>
                              <div className="text-xs text-gray-400">Growth</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-bold text-white">{tech.relevanceScore}%</div>
                              <div className="text-xs text-gray-400">Relevance</div>
                            </div>
                            <button className="bg-[#4FC3F7] text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-[#4FC3F7]/90 transition-colors">
                              Explore
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}

                {/* Report Generation CTA */}
                <div className="bg-gradient-to-r from-[#4FC3F7]/10 to-[#7C4DFF]/10 backdrop-blur-sm rounded-xl border border-[#4FC3F7]/30 p-8 text-center">
                  <h3 className="text-xl font-bold text-white mb-2">Get Personalized Report</h3>
                  <p className="text-gray-300 mb-6">
                    Detailed analysis tailored to your {industryOptions.find(i => i.key === selectedIndustry)?.label} business context
                  </p>
                  <button className="bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] text-white py-3 px-8 rounded-lg font-medium hover:opacity-90 transition-opacity">
                    Generate Report
                  </button>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PulseSection;
